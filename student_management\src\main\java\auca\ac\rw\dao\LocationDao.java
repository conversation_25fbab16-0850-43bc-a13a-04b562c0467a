package auca.ac.rw.dao;

import java.util.UUID;

import org.hibernate.Session;
import org.hibernate.Transaction;

import auca.ac.rw.model.Location;
import auca.ac.rw.util.HibernateUtil;

public class LocationDao {
    

    public String saveLocation(Location location, UUID parent_id){

        try(Session session = HibernateUtil.openConnection().openSession()){

            Transaction transaction = session.beginTransaction();

            if(parent_id == null){
                session.persist(location);
                transaction.commit();
                return "location saved successfully";
            }else{

                Location getParent = session.get(Location.class, parent_id);

                
                location.setParent(getParent);

                session.persist(location);
                transaction.commit();
                return "location saved successfully";
            }
                
        }catch(Exception ex){
            ex.printStackTrace();
            return null;
        }
    }
}
