package auca.ac.rw.dao;

import java.util.UUID;

import org.hibernate.Session;
import org.hibernate.Transaction;

import auca.ac.rw.model.ELocationType;
import auca.ac.rw.model.Location;
import auca.ac.rw.util.HibernateUtil;

public class LocationDao {

    public String saveLocation(Location location, UUID parent_id) {

        try (Session session = HibernateUtil.openConnection().openSession()) {

            Transaction transaction = session.beginTransaction();

            if (parent_id == null) {
                session.persist(location);
                transaction.commit();
                return "location saved successfully";
            } else {

                Location getParent = session.get(Location.class, parent_id);

                location.setParent(getParent);

                session.persist(location);
                transaction.commit();
                return "location saved successfully";
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }

    /**
     * Create a Province (top-level location with no parent)
     */
    public String createProvince(String code, String name) {
        Location province = new Location();
        province.setCode(code);
        province.setName(name);
        province.setType(ELocationType.PROVINCE);
        province.setParent(null); // Province has no parent

        return saveLocation(province, null);
    }

    /**
     * Create a District under a specific Province
     */
    public String createDistrict(String code, String name, UUID provinceId) {
        try (Session session = HibernateUtil.openConnection().openSession()) {
            Location province = session.get(Location.class, provinceId);
            if (province == null || province.getType() != ELocationType.PROVINCE) {
                return "Invalid province ID or location is not a province";
            }

            Location district = new Location();
            district.setCode(code);
            district.setName(name);
            district.setType(ELocationType.DISTRICT);

            return saveLocation(district, provinceId);
        } catch (Exception ex) {
            ex.printStackTrace();
            return "Error creating district: " + ex.getMessage();
        }
    }

    /**
     * Create a Sector under a specific District
     */
    public String createSector(String code, String name, UUID districtId) {
        try (Session session = HibernateUtil.openConnection().openSession()) {
            Location district = session.get(Location.class, districtId);
            if (district == null || district.getType() != ELocationType.DISTRICT) {
                return "Invalid district ID or location is not a district";
            }

            Location sector = new Location();
            sector.setCode(code);
            sector.setName(name);
            sector.setType(ELocationType.SECTOR);

            return saveLocation(sector, districtId);
        } catch (Exception ex) {
            ex.printStackTrace();
            return "Error creating sector: " + ex.getMessage();
        }
    }

    /**
     * Create a Cell under a specific Sector
     */
    public String createCell(String code, String name, UUID sectorId) {
        try (Session session = HibernateUtil.openConnection().openSession()) {
            Location sector = session.get(Location.class, sectorId);
            if (sector == null || sector.getType() != ELocationType.SECTOR) {
                return "Invalid sector ID or location is not a sector";
            }

            Location cell = new Location();
            cell.setCode(code);
            cell.setName(name);
            cell.setType(ELocationType.CELL);

            return saveLocation(cell, sectorId);
        } catch (Exception ex) {
            ex.printStackTrace();
            return "Error creating cell: " + ex.getMessage();
        }
    }

    /**
     * Create a Village under a specific Cell
     */
    public String createVillage(String code, String name, UUID cellId) {
        try (Session session = HibernateUtil.openConnection().openSession()) {
            Location cell = session.get(Location.class, cellId);
            if (cell == null || cell.getType() != ELocationType.CELL) {
                return "Invalid cell ID or location is not a cell";
            }

            Location village = new Location();
            village.setCode(code);
            village.setName(name);
            village.setType(ELocationType.VILLAGE);

            return saveLocation(village, cellId);
        } catch (Exception ex) {
            ex.printStackTrace();
            return "Error creating village: " + ex.getMessage();
        }
    }

    /**
     * Get province name by village ID
     * Traverses up the location hierarchy to find the province
     */
    public String getProvinceNameByVillageId(UUID villageId) {
        try (Session session = HibernateUtil.openConnection().openSession()) {
            Location village = session.get(Location.class, villageId);

            if (village == null) {
                return "Village not found";
            }

            if (village.getType() != ELocationType.VILLAGE) {
                return "Location is not a village";
            }

            // Traverse up the hierarchy: Village -> Cell -> Sector -> District -> Province
            Location current = village;
            while (current != null && current.getType() != ELocationType.PROVINCE) {
                current = current.getParent();
            }

            if (current != null && current.getType() == ELocationType.PROVINCE) {
                return current.getName();
            } else {
                return "Province not found in hierarchy";
            }

        } catch (Exception ex) {
            ex.printStackTrace();
            return "Error retrieving province: " + ex.getMessage();
        }
    }

    /**
     * Helper method to find a location by code and type
     * Useful for testing purposes
     */
    public Location findLocationByCodeAndType(String code, ELocationType type) {
        try (Session session = HibernateUtil.openConnection().openSession()) {
            return session.createQuery(
                    "FROM Location WHERE code = :code AND type = :type", Location.class)
                    .setParameter("code", code)
                    .setParameter("type", type)
                    .uniqueResult();
        } catch (Exception ex) {
            ex.printStackTrace();
            return null;
        }
    }
}
