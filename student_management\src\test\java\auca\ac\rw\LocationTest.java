package auca.ac.rw;

import static org.junit.Assert.assertEquals;

import java.util.UUID;

import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;// 

import auca.ac.rw.dao.LocationDao;
import auca.ac.rw.model.ELocationType;
import auca.ac.rw.model.Location;

public class LocationTest {

    LocationDao locationDao = new LocationDao();

    Location location = new Location();

    @Before
    public void setLocationDetail() {
        location.setCode("11111");
        location.setName("UBUMWE");
        location.setType(ELocationType.VILLAGE);
    }

    @Ignore
    @Test
    public void saveProvince() {
        String saveLocation = locationDao.saveLocation(location, null);
        assertEquals("location saved successfully", saveLocation);
    }

    @Test
    public void saveDistrict() {
        String saveLocation = locationDao.saveLocation(location,
                UUID.fromString("513feca3-c266-4515-b48c-5215796f942f"));
        assertEquals("location saved successfully", saveLocation);
    }
}
