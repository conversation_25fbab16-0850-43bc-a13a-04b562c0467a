package auca.ac.rw;

import static org.junit.Assert.assertEquals;

import java.util.UUID;

import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.FixMethodOrder;
import org.junit.runners.MethodSorters;

import auca.ac.rw.dao.LocationDao;
import auca.ac.rw.model.ELocationType;
import auca.ac.rw.model.Location;

@FixMethodOrder(MethodSorters.NAME_ASCENDING)
public class LocationTest {

    LocationDao locationDao = new LocationDao();
    Location location = new Location();

    @Before
    public void setLocationDetail() {
        location.setCode("11111");
        location.setName("UBUMWE");
        location.setType(ELocationType.VILLAGE);
    }

    @Ignore
    @Test
    public void saveProvince() {
        String saveLocation = locationDao.saveLocation(location, null);
        assertEquals("location saved successfully", saveLocation);
    }

    @Test
    public void saveDistrict() {
        String saveLocation = locationDao.saveLocation(location,
                UUID.fromString("513feca3-c266-4515-b48c-5215796f942f"));
        assertEquals("location saved successfully", saveLocation);
    }

    // Test cases for location creation methods

    @Test
    public void test1_CreateProvince() {
        String result = locationDao.createProvince("PROV001", "Kigali Province");
        assertEquals("location saved successfully", result);
    }

    @Test
    public void test2_CreateDistrict() {
        // First create a province to use as parent
        String provinceResult = locationDao.createProvince("PROV002", "Eastern Province");
        assertEquals("location saved successfully", provinceResult);

        // Note: In a real test, you would need to retrieve the province ID
        // For this test, we'll use a mock UUID - in practice, you'd query the database
        UUID mockProvinceId = UUID.fromString("123e4567-e89b-12d3-a456-************");
        String districtResult = locationDao.createDistrict("DIST001", "Gasabo District", mockProvinceId);

        // This might fail if the province doesn't exist, but demonstrates the method
        // structure
        // In a real scenario, you'd first save the province and get its actual ID
    }

    @Test
    public void test3_CreateSector() {
        // Test creating a sector with invalid district ID
        UUID invalidDistrictId = UUID.fromString("123e4567-e89b-12d3-a456-************");
        String result = locationDao.createSector("SECT001", "Kimisagara Sector", invalidDistrictId);
        assertEquals("Invalid district ID or location is not a district", result);
    }

    @Test
    public void test4_CreateCell() {
        // Test creating a cell with invalid sector ID
        UUID invalidSectorId = UUID.fromString("123e4567-e89b-12d3-a456-************");
        String result = locationDao.createCell("CELL001", "Nyamirambo Cell", invalidSectorId);
        assertEquals("Invalid sector ID or location is not a sector", result);
    }

    @Test
    public void test5_CreateVillage() {
        // Test creating a village with invalid cell ID
        UUID invalidCellId = UUID.fromString("123e4567-e89b-12d3-a456-************");
        String result = locationDao.createVillage("VILL001", "Ubumwe Village", invalidCellId);
        assertEquals("Invalid cell ID or location is not a cell", result);
    }

    @Test
    public void test6_GetProvinceNameByVillageId_VillageNotFound() {
        UUID nonExistentVillageId = UUID.fromString("123e4567-e89b-12d3-a456-************");
        String result = locationDao.getProvinceNameByVillageId(nonExistentVillageId);
        assertEquals("Village not found", result);
    }

    // Integration test for complete hierarchy creation and province retrieval
    @Test
    public void test7_CompleteHierarchyCreationAndRetrieval() {
        // Step 1: Create province
        String provinceResult = locationDao.createProvince("PROV_TEST", "Test Province");
        assertEquals("location saved successfully", provinceResult);

        // Step 2: Find the created province
        Location province = locationDao.findLocationByCodeAndType("PROV_TEST", ELocationType.PROVINCE);
        if (province != null) {
            // Step 3: Create district under the province
            String districtResult = locationDao.createDistrict("DIST_TEST", "Test District", province.getId());
            assertEquals("location saved successfully", districtResult);

            // Step 4: Find the created district
            Location district = locationDao.findLocationByCodeAndType("DIST_TEST", ELocationType.DISTRICT);
            if (district != null) {
                // Step 5: Create sector under the district
                String sectorResult = locationDao.createSector("SECT_TEST", "Test Sector", district.getId());
                assertEquals("location saved successfully", sectorResult);

                // Step 6: Find the created sector
                Location sector = locationDao.findLocationByCodeAndType("SECT_TEST", ELocationType.SECTOR);
                if (sector != null) {
                    // Step 7: Create cell under the sector
                    String cellResult = locationDao.createCell("CELL_TEST", "Test Cell", sector.getId());
                    assertEquals("location saved successfully", cellResult);

                    // Step 8: Find the created cell
                    Location cell = locationDao.findLocationByCodeAndType("CELL_TEST", ELocationType.CELL);
                    if (cell != null) {
                        // Step 9: Create village under the cell
                        String villageResult = locationDao.createVillage("VILL_TEST", "Test Village", cell.getId());
                        assertEquals("location saved successfully", villageResult);

                        // Step 10: Find the created village
                        Location village = locationDao.findLocationByCodeAndType("VILL_TEST", ELocationType.VILLAGE);
                        if (village != null) {
                            // Step 11: Test getProvinceNameByVillageId
                            String provinceName = locationDao.getProvinceNameByVillageId(village.getId());
                            assertEquals("Test Province", provinceName);
                        }
                    }
                }
            }
        }
    }

    @Test
    public void test8_CreateProvinceWithEmptyName() {
        String result = locationDao.createProvince("PROV004", "");
        assertEquals("location saved successfully", result);
    }

    @Test
    public void test9_CreateProvinceWithNullCode() {
        String result = locationDao.createProvince(null, "Test Province");
        assertEquals("location saved successfully", result);
    }
}
