<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="auca.ac.rw.AppTest" time="0.014" tests="1" errors="0" skipped="0" failures="1">
  <properties>
    <property name="java.specification.version" value="23"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/Users/<USER>/Desktop/AUCA/Summer testing 2025/Tuesday/student_management/student_management/target/test-classes:/Users/<USER>/Desktop/AUCA/Summer testing 2025/Tuesday/student_management/student_management/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.11/junit-4.11.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.20.Final/hibernate-core-6.6.20.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.5.0.Final/jboss-logging-3.5.0.Final.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.0/jakarta.xml.bind-api-4.0.0.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.0/jakarta.activation-api-2.1.0.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.2/jaxb-runtime-4.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.2/jaxb-core-4.0.2.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.0/angus-activation-2.0.0.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.2/txw2-4.0.2.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.1/istack-commons-runtime-4.1.1.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/postgresql/postgresql/42.4.4/postgresql-42.4.4.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.5.0/checker-qual-3.5.0.jar:"/>
    <property name="java.vm.vendor" value="Homebrew"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="os.name" value="Mac OS X"/>
    <property name="java.vm.specification.version" value="23"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="RW"/>
    <property name="sun.boot.library.path" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home/lib"/>
    <property name="sun.java.command" value="/Users/<USER>/Desktop/AUCA/Summer testing 2025/Tuesday/student_management/student_management/target/surefire/surefirebooter17964765458022198112.jar /Users/<USER>/Desktop/AUCA/Summer testing 2025/Tuesday/student_management/student_management/target/surefire 2025-07-22T19-02-36_733-jvmRun1 surefire8939484289065077091tmp surefire_015441217045763986054tmp"/>
    <property name="http.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="/Users/<USER>/Desktop/AUCA/Summer testing 2025/Tuesday/student_management/student_management/target/test-classes:/Users/<USER>/Desktop/AUCA/Summer testing 2025/Tuesday/student_management/student_management/target/classes:/Users/<USER>/.m2/repository/junit/junit/4.11/junit-4.11.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.6.20.Final/hibernate-core-6.6.20.Final.jar:/Users/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/Users/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.5.0.Final/jboss-logging-3.5.0.Final.jar:/Users/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/7.0.3.Final/hibernate-commons-annotations-7.0.3.Final.jar:/Users/<USER>/.m2/repository/io/smallrye/jandex/3.2.0/jandex-3.2.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.15.11/byte-buddy-1.15.11.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.0/jakarta.xml.bind-api-4.0.0.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.0/jakarta.activation-api-2.1.0.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.2/jaxb-runtime-4.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.2/jaxb-core-4.0.2.jar:/Users/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.0/angus-activation-2.0.0.jar:/Users/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.2/txw2-4.0.2.jar:/Users/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.1/istack-commons-runtime-4.1.1.jar:/Users/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/Users/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.13.0/antlr4-runtime-4.13.0.jar:/Users/<USER>/.m2/repository/org/postgresql/postgresql/42.4.4/postgresql-42.4.4.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.5.0/checker-qual-3.5.0.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/Users/<USER>"/>
    <property name="user.language" value="en"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-01-21"/>
    <property name="java.home" value="/opt/homebrew/Cellar/openjdk/23.0.2/libexec/openjdk.jdk/Contents/Home"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/Users/<USER>/Desktop/AUCA/Summer testing 2025/Tuesday/student_management/student_management"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="apple.awt.application.name" value="ForkedBooter"/>
    <property name="surefire.real.class.path" value="/Users/<USER>/Desktop/AUCA/Summer testing 2025/Tuesday/student_management/student_management/target/surefire/surefirebooter17964765458022198112.jar"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="ftp.nonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.runtime.version" value="23.0.2"/>
    <property name="user.name" value="patrick"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="13.0"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Homebrew"/>
    <property name="localRepository" value="/Users/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/Homebrew/homebrew-core/issues"/>
    <property name="java.io.tmpdir" value="/var/folders/w1/73_r8p2509v3jk87d8dbk7xh0000gn/T/"/>
    <property name="java.version" value="23.0.2"/>
    <property name="user.dir" value="/Users/<USER>/Desktop/AUCA/Summer testing 2025/Tuesday/student_management/student_management"/>
    <property name="os.arch" value="aarch64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vendor" value="Homebrew"/>
    <property name="java.vm.version" value="23.0.2"/>
    <property name="sun.io.unicode.encoding" value="UnicodeBig"/>
    <property name="socksNonProxyHosts" value="local|*.local|169.254/16|*.169.254/16"/>
    <property name="java.class.version" value="67.0"/>
  </properties>
  <testcase name="testCheckEvenNumbersInDesc" classname="auca.ac.rw.AppTest" time="0.003">
    <failure message="actual array was null" type="java.lang.AssertionError">java.lang.AssertionError: actual array was null
	at auca.ac.rw.AppTest.testCheckEvenNumbersInDesc(AppTest.java:62)
</failure>
  </testcase>
</testsuite>