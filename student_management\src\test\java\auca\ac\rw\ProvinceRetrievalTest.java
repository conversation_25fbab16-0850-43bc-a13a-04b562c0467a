package auca.ac.rw;

import static org.junit.Assert.assertEquals;

import java.util.UUID;

import org.junit.Before;
import org.junit.Test;

import auca.ac.rw.dao.LocationDao;
import auca.ac.rw.model.ELocationType;
import auca.ac.rw.model.Location;

/**
 * Dedicated test class for testing the getProvinceNameByVillageId method
 */
public class ProvinceRetrievalTest {

    private LocationDao locationDao;

    @Before
    public void setUp() {
        locationDao = new LocationDao();
    }

    @Test
    public void testGetProvinceNameByVillageId_ValidVillage() {
        // Create a complete hierarchy for testing
        String provinceName = "Kigali Province";
        
        // Step 1: Create province
        String provinceResult = locationDao.createProvince("KGL_PROV", provinceName);
        assertEquals("location saved successfully", provinceResult);
        
        // Find the created province
        Location province = locationDao.findLocationByCodeAndType("KGL_PROV", ELocationType.PROVINCE);
        
        if (province != null) {
            // Step 2: Create district
            String districtResult = locationDao.createDistrict("KGL_DIST", "Gasabo District", province.getId());
            assertEquals("location saved successfully", districtResult);
            
            Location district = locationDao.findLocationByCodeAndType("KGL_DIST", ELocationType.DISTRICT);
            
            if (district != null) {
                // Step 3: Create sector
                String sectorResult = locationDao.createSector("KGL_SECT", "Kimisagara Sector", district.getId());
                assertEquals("location saved successfully", sectorResult);
                
                Location sector = locationDao.findLocationByCodeAndType("KGL_SECT", ELocationType.SECTOR);
                
                if (sector != null) {
                    // Step 4: Create cell
                    String cellResult = locationDao.createCell("KGL_CELL", "Nyamirambo Cell", sector.getId());
                    assertEquals("location saved successfully", cellResult);
                    
                    Location cell = locationDao.findLocationByCodeAndType("KGL_CELL", ELocationType.CELL);
                    
                    if (cell != null) {
                        // Step 5: Create village
                        String villageResult = locationDao.createVillage("KGL_VILL", "Ubumwe Village", cell.getId());
                        assertEquals("location saved successfully", villageResult);
                        
                        Location village = locationDao.findLocationByCodeAndType("KGL_VILL", ELocationType.VILLAGE);
                        
                        if (village != null) {
                            // Test the main method
                            String retrievedProvinceName = locationDao.getProvinceNameByVillageId(village.getId());
                            assertEquals(provinceName, retrievedProvinceName);
                        }
                    }
                }
            }
        }
    }

    @Test
    public void testGetProvinceNameByVillageId_NonExistentVillage() {
        UUID nonExistentVillageId = UUID.fromString("00000000-0000-0000-0000-000000000000");
        String result = locationDao.getProvinceNameByVillageId(nonExistentVillageId);
        assertEquals("Village not found", result);
    }

    @Test
    public void testGetProvinceNameByVillageId_NotAVillage() {
        // Create a province and try to get province name using province ID
        String provinceResult = locationDao.createProvince("TEST_PROV", "Test Province");
        assertEquals("location saved successfully", provinceResult);
        
        Location province = locationDao.findLocationByCodeAndType("TEST_PROV", ELocationType.PROVINCE);
        
        if (province != null) {
            String result = locationDao.getProvinceNameByVillageId(province.getId());
            assertEquals("Location is not a village", result);
        }
    }

    @Test
    public void testGetProvinceNameByVillageId_IncompleteHierarchy() {
        // Create a village without proper hierarchy (this would be an edge case)
        // In practice, this shouldn't happen due to foreign key constraints
        // but we test the method's robustness
        
        // Create a standalone village (this might fail due to constraints)
        // This test demonstrates what would happen if hierarchy is broken
        UUID randomId = UUID.randomUUID();
        String result = locationDao.getProvinceNameByVillageId(randomId);
        assertEquals("Village not found", result);
    }

    @Test
    public void testGetProvinceNameByVillageId_MultipleProvinces() {
        // Test with a different province to ensure method works for multiple provinces
        String provinceName = "Eastern Province";
        
        // Create another complete hierarchy
        String provinceResult = locationDao.createProvince("EAST_PROV", provinceName);
        assertEquals("location saved successfully", provinceResult);
        
        Location province = locationDao.findLocationByCodeAndType("EAST_PROV", ELocationType.PROVINCE);
        
        if (province != null) {
            String districtResult = locationDao.createDistrict("EAST_DIST", "Rwamagana District", province.getId());
            assertEquals("location saved successfully", districtResult);
            
            Location district = locationDao.findLocationByCodeAndType("EAST_DIST", ELocationType.DISTRICT);
            
            if (district != null) {
                String sectorResult = locationDao.createSector("EAST_SECT", "Rwamagana Sector", district.getId());
                assertEquals("location saved successfully", sectorResult);
                
                Location sector = locationDao.findLocationByCodeAndType("EAST_SECT", ELocationType.SECTOR);
                
                if (sector != null) {
                    String cellResult = locationDao.createCell("EAST_CELL", "Rwamagana Cell", sector.getId());
                    assertEquals("location saved successfully", cellResult);
                    
                    Location cell = locationDao.findLocationByCodeAndType("EAST_CELL", ELocationType.CELL);
                    
                    if (cell != null) {
                        String villageResult = locationDao.createVillage("EAST_VILL", "Rwamagana Village", cell.getId());
                        assertEquals("location saved successfully", villageResult);
                        
                        Location village = locationDao.findLocationByCodeAndType("EAST_VILL", ELocationType.VILLAGE);
                        
                        if (village != null) {
                            String retrievedProvinceName = locationDao.getProvinceNameByVillageId(village.getId());
                            assertEquals(provinceName, retrievedProvinceName);
                        }
                    }
                }
            }
        }
    }
}
