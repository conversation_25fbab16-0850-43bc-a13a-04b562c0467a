package auca.ac.rw;

import static org.junit.Assert.assertArrayEquals;

/**
 * Unit test for simple App.
 */
public class AppTest {

  // Calculation calculation = new Calculation();

  // AgeCalculation ageCalculation = new AgeCalculation();

  // int age = 0;

  // @Before
  // public void assignValue(){
  // age = 30;
  // }

  // @Test
  // public void testChild(){
  // System.out.println("age"+age);
  // String result = ageCalculation.checkTheAge(age);
  // assertEquals("adult", result);
  // }

  EvenNumber evenNumber = new EvenNumber();

  int[] result;

  // @Before
  public void testEvenumbers() {
    int[] numbers = { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10 };

    int[] expectedEvenNumbers = { 2, 4, 6, 8, 10 };

    result = evenNumber.checkEvenNumbers(numbers);
    assertArrayEquals(expectedEvenNumbers, result);

  }

  // @Test
  public void testCheckEvenNumbersInDesc() {
    int[] expectedEvenNumbers = { 10, 8, 6, 4, 2 };

    int[] Newresult = evenNumber.checkEvenNumbersDesc(result);
    assertArrayEquals(expectedEvenNumbers, Newresult);
  }

}
