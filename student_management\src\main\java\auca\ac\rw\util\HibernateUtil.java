package auca.ac.rw.util;

import java.util.Properties;


import org.hibernate.SessionFactory;
import org.hibernate.cfg.Configuration;
import org.hibernate.cfg.Environment;

import auca.ac.rw.model.Location;
import auca.ac.rw.model.Student;

public class HibernateUtil {
    

    private  static SessionFactory sessionFactory  = null;

    public static SessionFactory openConnection(){
        
        try{
        if(sessionFactory == null){
            Configuration configuration = new Configuration();

            Properties properties = new Properties();

            properties.put(Environment.JAKARTA_JDBC_DRIVER,"org.postgresql.Driver");
            properties.put(Environment.JAKARTA_JDBC_URL, "************************************************");
            properties.put(Environment.JAKARTA_JDBC_USER,"postgres");
            properties.put(Environment.JAKARTA_JDBC_PASSWORD, "prisca");
            properties.put(Environment.SHOW_SQL, "true");

            properties.put(Environment.HBM2DDL_AUTO, "update");

            configuration.setProperties(properties);

            configuration.addAnnotatedClass(Student.class);
            configuration.addAnnotatedClass(Location.class);

            sessionFactory = configuration.buildSessionFactory();
        }
    }catch(Exception ex){
        ex.printStackTrace();
    }

    return sessionFactory;
    }
}
